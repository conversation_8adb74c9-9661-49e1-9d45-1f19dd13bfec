import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistensiTeamController extends BaseControllers {
  // Changed to store a list of persistence data for multiple agents
  RxList<PersistensiModel> persistensiDataList = <PersistensiModel>[].obs;
  late SharedPreferences prefs;

  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  ScrollController scrollController = ScrollController();

  @override
  void onInit() {
    super.onInit();
    setupScrollListener();
  }

  void setupScrollListener() {
    print('here start setup');
    scrollController.addListener(() {
      print('start listening');
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels == 0) {
          // You're at the top.
        } else {
          // You're at the bottom.
          print('reaching End');
        }
      }
    });
  }

  fetchPersistensiData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    prefs = await SharedPreferences.getInstance();
    String agentCode = prefs.getString(kStorageAgentCode) ?? '';
    // Get agent code from URL parameter if available
    if (Get.parameters.containsKey('agentCode')) {
      agentCode = Get.parameters['agentCode'] ?? '';
    }

    final currentYear = DateTime.now().year.toString();

    try {
      await api.getPersistencyTeam(
        controllers: this,
        code: kReqGetPersistencyTeam,
        params: "agentCode=$agentCode&year=$currentYear",
      );
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error fetching data: $e';
      setLoading(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    parseData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = response.statusText ?? 'Error loading data';
    setLoading(false);
  }

  void parseData(response) {
    if (response == null || (response is List && response.isEmpty)) {
      hasError.value = true;
      errorMessage.value = 'No data available';
      return;
    }

    try {
      // Clear previous data
      persistensiDataList.clear();

      // Parse all items in the response list
      for (var item in response['content']) {
        PersistensiModel parsedData = PersistensiModel.fromJson(item);
        persistensiDataList.add(parsedData);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error parsing data: $e';
    }
  }

  // Helper method to format percentage values
  String formatPercentage(double value) {
    return '${value.toStringAsFixed(2)}%';
  }
}
