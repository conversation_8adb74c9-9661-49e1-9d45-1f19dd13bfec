import 'dart:io';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_api.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/body/approval_body.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

String baseUrl = ConfigReader.getBaseUrl();
String commonUrl = ConfigReader.getPublicUrl();

class Api extends BaseApi {
  // declare URL
  final String _loginUrl = '$baseUrl/auth/login';
  final String _logoutUrl = '$baseUrl/auth/logout';
  final String _auth = '$baseUrl/auth';
  final String _profileUrl = '$baseUrl/profile';
  final String _device = '$baseUrl/device';

  // Widgets URL
  final String _widget = '$baseUrl/widget';
  final String _promosiAgent = '$baseUrl/widget/promosi-agent';
  final String _promosiLeader = '$baseUrl/widget/promosi-leader';
  final String _validasiHirarki = '$baseUrl/widget/validasi-hirarki';
  final String _validasiG1 = '$baseUrl/widget/validasi-g1';
  final String _commission = '$baseUrl/widget/commission';
  final String _detailCommission = '$baseUrl/widget/detail-commission';
  final String _persistencyIndividu = '$baseUrl/widget/persistency-individual';
  final String _persistencyTeam = '$baseUrl/widget/persistency-team';
  final String _menuUrl = '$commonUrl/menu';

  // public URL
  final String _flyer = '$commonUrl/public/flyer';
  final String _information = '$commonUrl/public/information';
  final String _question = '$commonUrl/public/question';
  final String _theme = '$commonUrl/public/theme/current';
  final String _globalConfig = '$commonUrl/public/globalConfig';

  // cms URL
  final String _cms = '$baseUrl/cms';

  // Recruitment
  final String _recruitment = '$baseUrl/agency/recruitment';
  final String _recruitmentPublic = '$baseUrl/public/agency/recruitment';
  final String _comboCategory = '$commonUrl/public/combo-category';
  final String _branch = '$baseUrl/branch';
  final String _bank = '$baseUrl/bank';

  //INBOX URL
  final String _inbox = '$baseUrl/inbox';

  //APPROVAL URL
  final String _approval = '$baseUrl/cms/approval';
  final String _approvalHistory = '$baseUrl/cms/approval/history';

  //REJOIN URL
  final String _rejoin = '$baseUrl/agency/rejoin';
  final String _rejoinEligibleCandidates =
      '$baseUrl/agency/rejoin/eligible-candidates';

  String mapToQueryParameters(Map<String, dynamic> params) {
    final entries = params.entries.map((e) => '${e.key}=${e.value}');
    return '?${entries.join('&')}';
  }

  // termination
  final String _agentTermination = '$baseUrl/agency/terminations';
  final String _agentTerminationCandidates =
      '$baseUrl/agency/terminations/eligible-candidates';
  final String _agentPolicyTransfer = '$baseUrl/agency/policy-transfer';

  Future<void> performLogin({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: _loginUrl,
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performLogout({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiPost(
      url: _logoutUrl,
      controller: controllers,
      data: {},
      debug: false,
      code: code ?? 0,
    );
  }

  // Auth
  Future<void> performForgotPassword({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_auth/forget-password',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performResetPassword({
    required BaseControllers controllers,
    required var data,
    required String token,
    int? code,
  }) async {
    await apiPost(
      url: '$_auth/reset-password/$token',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  // Profile
  Future<void> getProfile({
    required BaseControllers controllers,
    int? code,
    bool? debug,
  }) async {
    await apiFetch(
      url: _profileUrl,
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> getSimpleProfile({
    required BaseControllers controllers,
    required String agentCode,
    int? code,
  }) async {
    await apiFetch(
      url: '$_profileUrl/simple/$agentCode',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> patchProfile({
    required BaseControllers controllers,
    var data,
    int? code,
  }) async {
    await apiPatch(
      url: '$_profileUrl/request',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> patchReviseProfile({
    required BaseControllers controllers,
    required String id,
    var data,
    int? code,
  }) async {
    await apiPatch(
      url: '$_profileUrl/request/revise/$id',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> updateProfileCms({
    required BaseControllers controllers,
    var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_cms/profile',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getProfileRequestStatus({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_profileUrl/request$params',
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performChangePassword({
    required BaseControllers controllers,
    var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_profileUrl/change-password',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> generateContractDocuments({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiPost(
      url: '$_profileUrl/contract-documents/generate',
      controller: controllers,
      data: {},
      code: code ?? 0,
      debug: false,
    );
  }

  // Device
  Future<void> getDeviceList({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: _device,
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performRegisterDevice({
    required BaseControllers controllers,
    var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_device/register',
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> performRevokeDevice({
    required BaseControllers controllers,
    required String deviceId,
    int? code,
  }) async {
    await apiPost(
      url: '$_device/revoke/$deviceId',
      controller: controllers,
      data: {},
      code: code ?? 0,
      debug: false,
    );
  }

  // Public
  Future<void> getFlyer({
    required BaseControllers controllers,
    int? code,
    isToday = false,
  }) async {
    var url = '$_flyer?isActive=true';
    if (isToday) {
      var now = DateTime.now();
      url += "&startDate=${DateFormat("yyyy-MM-dd").format(now)}";
      var tomorrow = DateTime.now().add(Duration(days: 1));
      url += "&endDate=${DateFormat("yyyy-MM-dd").format(tomorrow)}";
    }
    await apiFetch(
      url: url,
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getInformation({
    required BaseControllers controllers,
    required String infoType,
    int? code,
  }) async {
    await apiFetch(
      url: '$_information?informationType=$infoType',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getQuestion({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_question?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getQuestionDetail({
    required BaseControllers controllers,
    required String id,
    int? code,
  }) async {
    await apiFetch(
      url: '$_question/$id',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Get current theme
  Future<void> getTheme({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: _theme,
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getPromosiAgent({
    required BaseControllers controllers,
    String? params,
    int? code,
    String? userLevel,
  }) async {
    // Use promosi-leader endpoint for BD level users
    String endpoint =
        [kLevelBD, kLevelBM].contains(userLevel)
            ? _promosiLeader
            : _promosiAgent;
    await apiFetch(
      url: '$endpoint?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getValidasiHirarki({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_validasiHirarki?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getValidasiG1({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_validasiG1?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getCommission({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_commission${params != null ? '?$params' : ''}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getDetailCommission({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_detailCommission${params != null ? '?$params' : ''}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Strat OF Widget API
  Future<void> getWidgetProductionSum({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/summary-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionDetail({
    required BaseControllers controllers,
    String? params,
    int? code,
    bool? debug,
  }) async {
    await apiFetch(
      url: '$_widget/detail-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: debug ?? false,
    );
  }

  Future<void> getWidgetProductionMy({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/my-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionTeam({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/team-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionGroup({
    required BaseControllers controllers,
    String? params,
    int? code,
    bool? debug,
  }) async {
    await apiFetch(
      url: '$_widget/group-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: debug ?? true,
    );
  }

  Future<void> getWidgetProductionBranch({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/branch-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionAreaBdm({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/bdm-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionAreaAbdd({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/abdd-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionAreaBdd({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/bdd-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetProductionAreaHos({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/hos-production?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getProductionGraphicDataYear({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/yearly-summary-production',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getProductionGraphicDataMonth({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/monthly-summary-production?year=${DateTime.now().year}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  /// Production graphic data with agentCode parameter for detail pages
  Future<void> getProductionGraphicDataYearWithAgent({
    required BaseControllers controllers,
    required String agentCode,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/yearly-summary-production?agentCode=$agentCode',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getProductionGraphicDataMonthWithAgent({
    required BaseControllers controllers,
    required String agentCode,
    int? code,
  }) async {
    await apiFetch(
      url:
          '$_widget/monthly-summary-production?year=${DateTime.now().year}&agentCode=$agentCode',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Persistency API
  Future<void> getPersistencyIndividu({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_persistencyIndividu/pageable?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getPersistencyTeam({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_persistencyTeam/pageable?$params',
      controller: controllers,
      code: code ?? 0,
      debug: true,
    );
  }

  Future<void> getSpajIndividu({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/spaj-individual/pageable?${params ?? ''}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getSpajTeam({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/spaj-team/pageable?${params ?? ''}',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getStatusClaim({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/claim-tracking/pageable?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getPolicyLapsed({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/policy-lapsed/pageable?$params',
      controller: controllers,
      code: code ?? 0,
      debug: true,
    );
  }

  Future<void> getPolicyOverdue({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/policy-overdue/pageable?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getBirthdayCustomer({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_widget/birthday-customer/pageable?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getBirthdayTemplate({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_globalConfig/birthday.template',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getWidgetSort({
    required BaseControllers controllers,
    int? code,
    required String channel,
  }) async {
    await apiFetch(
      url: '$_menuUrl?menuType=WIDGET&isActive=true&channel=$channel',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  //inbox
  Future<void> getInbox({
    required BaseControllers controllers,
    Map<String, dynamic>? query,
    int? code,
  }) async {
    String params = '';
    if (query != null) {
      params = mapToQueryParameters(query);
    }

    await apiFetch(
      url: '$_inbox$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getCountUnreadInbox({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: '$_inbox/count/unread-by-trx-type',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> postReadInbox({
    required BaseControllers controllers,
    required List ids,
    int? code,
  }) async {
    await apiPost(
      url: '$_inbox/bulk/read',
      controller: controllers,
      data: {'inboxIds': ids},
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> postArchiveInbox({
    required BaseControllers controllers,
    required List<String> ids,
    int? code,
  }) async {
    await apiPost(
      url: '$_inbox/bulk/archive',
      controller: controllers,
      data: {'inboxIds': ids},
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> deleteInbox({
    required BaseControllers controllers,
    required List<String> ids,
    required bool isHardDelete,
    int? code,
  }) async {
    if (isHardDelete) {
      await apiDelete(
        url: '$_inbox/bulk',
        controller: controllers,
        data: {'inboxIds': ids},
        code: code ?? 0,
        debug: false,
      );
    } else {
      await apiPost(
        url: '$_inbox/bulk/soft-delete',
        controller: controllers,
        data: {'inboxIds': ids},
        code: code ?? 0,
        debug: false,
      );
    }
  }

  Future<void> deleteInboxSingle({
    required BaseControllers controllers,
    required int id,
    int? code,
  }) async {
    await apiDelete(
      url: '$_inbox/$id',
      controller: controllers,
      data: {},
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getDetailInbox({
    required BaseControllers controllers,
    required String id,
    int? code,
  }) async {
    await apiFetch(
      url: '$_inbox/$id',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  //REJOIN ------
  Future<void> getEligibleCandidatesRejoin({
    required BaseControllers controllers,
    Map<String, dynamic>? query,
    int? code,
  }) async {
    String params = '';
    if (query != null) {
      params = mapToQueryParameters(query);
    }
    await apiFetch(
      url: '$_rejoinEligibleCandidates$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getRequestedRejoin({
    required BaseControllers controllers,
    Map<String, dynamic>? query,
    int? code,
  }) async {
    String params = '';
    if (query != null) {
      params = mapToQueryParameters(query);
    }
    await apiFetch(
      url: '$_rejoin$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getDetailRejoin({
    required BaseControllers controllers,
    required int id,
    int? code,
  }) async {
    await apiFetch(
      url: '$_rejoin/$id',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> postRequestRejoin({
    required BaseControllers controllers,
    int? code,
    Map<String, dynamic>? data,
  }) async {
    await apiPost(
      url: _rejoin,
      controller: controllers,
      data: data,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> putCancelRejoin({
    required BaseControllers controllers,
    int? code,
    required int id,
    required String reason,
  }) async {
    await apiPut(
      url: '$_rejoin/cancel',
      controller: controllers,
      data: {'id': id, 'remarks': reason},
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getListApprovalRejoin({
    required BaseControllers controllers,
    int? code,
    required String id,
  }) async {
    await apiFetch(
      url: '$_approval/$id',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> postApproval({
    required BaseControllers controllers,
    int? code,
    required ApprovalBody body,
  }) async {
    await apiPost(
      url: _approval,
      controller: controllers,
      code: code ?? 0,
      debug: false,
      data: body.toJson(),
    );
  }

  Future<void> getListApproval({
    required BaseControllers controllers,
    String params = '',
    int? code,
  }) async {
    await apiFetch(
      url: '$_approval$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getListApprovalHistory({
    required BaseControllers controllers,
    String params = '',
    int? code,
  }) async {
    await apiFetch(
      url: '$_approvalHistory$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  //END REJOIN ----

  // Form
  Future<void> getBranch({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_branch?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getBank({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_bank?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Combo
  Future<void> getComboCategory({
    required BaseControllers controllers,
    int? code,
  }) async {
    await apiFetch(
      url: _comboCategory,
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getComboCategoryById({
    required BaseControllers controllers,
    required String key,
    int? code,
    bool? debug,
  }) async {
    await apiFetch(
      url: '$_comboCategory/$key',
      controller: controllers,
      code: code ?? 0,
      debug: debug ?? false,
    );
  }

  // Recruitment
  Future<Map<String, dynamic>?> uploadRecruitmentImage({
    required dynamic imageFile, // Can be File or XFile
    required String type,
    Function(double)? onProgress,
  }) async {
    try {
      // Check internet connection first
      final hasConnection = await _checkInternetConnection();
      if (!hasConnection) {
        log("Tidak ada koneksi internet untuk upload gambar");
        return null;
      }

      // Create form data
      MultipartFile multipartFile;
      if (imageFile is XFile) {
        // Handle XFile (web) - need to read bytes
        final bytes = await imageFile.readAsBytes();
        multipartFile = MultipartFile(bytes, filename: imageFile.name);
      } else {
        // Handle File (mobile)
        multipartFile = MultipartFile(
          imageFile,
          filename: imageFile.path.split('/').last,
        );
      }

      final form = FormData({'file': multipartFile});

      // Get token
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';

      // Upload to API
      final response = await GetConnect().post(
        '$_recruitment/upload/$type',
        form,
        headers: {'Authorization': 'Bearer $token'},
        uploadProgress: (percent) {
          if (onProgress != null) {
            onProgress(percent);
          }
        },
      );

      if (response.isOk && response.body != null) {
        // Parse response to get URL
        final responseData = response.body;
        String? uploadedUrl;

        // Handle different response formats
        if (responseData is Map<String, dynamic>) {
          uploadedUrl = response.body['initialPreview'][0];
        } else if (responseData is String) {
          uploadedUrl = response.body['initialPreview'][0];
        }

        if (uploadedUrl != null && uploadedUrl.isNotEmpty) {
          log("Upload berhasil untuk $type: $uploadedUrl");
          return {
            'success': true,
            'url': uploadedUrl,
            'message': 'Upload berhasil',
          };
        } else {
          log("Upload gagal: URL tidak ditemukan dalam response");
          return {
            'success': false,
            'url': null,
            'message': 'URL tidak ditemukan dalam response',
          };
        }
      } else {
        log("Upload gagal: ${response.statusText}");
        return {
          'success': false,
          'url': null,
          'message': response.statusText ?? 'Upload gagal',
        };
      }
    } catch (e) {
      log("Error saat upload gambar $type: $e");
      return {'success': false, 'url': null, 'message': 'Error: $e'};
    }
  }

  // Check internet connection
  Future<bool> _checkInternetConnection() async {
    try {
      if (kIsWeb) {
        // // On web, use a simple HTTP request to check connectivity
        // final response = await GetConnect()
        //     .get(
        //       'https://www.google.com',
        //       headers: {'Cache-Control': 'no-cache'},
        //     )
        //     .timeout(const Duration(seconds: 2));
        // return response.isOk;

        // web doesnt need internet connection check
        return true;
      } else {
        // On mobile, use InternetAddress.lookup
        final result = await InternetAddress.lookup('google.com');
        return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      }
    } catch (e) {
      log("Error checking internet connection: $e");
      return false;
    }
  }

  Future<void> getRecruitmentList({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_recruitment?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getProductivityData({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: '$_recruitment/productivity?$params',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getRecruitmentDetail({
    required BaseControllers controllers,
    required String uuid,
    int? code,
  }) async {
    await apiFetch(
      url: '$_recruitment/$uuid',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> performSaveDraft({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: '$_recruitment/draft',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performSubmitRecruitmentForm({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    log('Submit data here: $data');
    await apiPost(
      url: '$_recruitment/submit',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performPatchRecruitmentForm({
    required BaseControllers controllers,
    required var data,
    required String uuid,
    int? code,
  }) async {
    await apiPatch(
      url: '$_recruitment/revise/$uuid',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performSubmitInterview({
    required BaseControllers controllers,
    required var data,
    required String uuid,
    int? code,
  }) async {
    await apiPost(
      url: '$_recruitment/$uuid/interview',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performDraftRecruitmentFormPublic({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: '$baseUrl/public/agency/recruitment/draft',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performSubmitRecruitmentFormPublic({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: '$baseUrl/public/agency/recruitment/submit',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> performSendVerifEmailPublic({
    required BaseControllers controllers,
    required String uuid,
    int? code,
  }) async {
    await apiPost(
      url: '$_recruitmentPublic/$uuid/send-verification-email',
      controller: controllers,
      data: {},
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> verifyEmail({
    required BaseControllers controllers,
    required String token,
    int? code,
  }) async {
    await apiFetch(
      url: '$_recruitmentPublic/verify-email?token=$token',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getIsVerifiedPublic({
    required BaseControllers controllers,
    required String uuid,
    int? code,
  }) async {
    await apiFetch(
      url: '$_recruitmentPublic/check-email-verified?uuid=$uuid',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> performPatchSingaturePublic({
    required BaseControllers controllers,
    required String uuid,
    required data,
    int? code,
  }) async {
    await apiPatch(
      url: '$_recruitmentPublic/sign/$uuid',
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  // Send verification email
  Future<void> performSendVerifEmail({
    required BaseControllers controllers,
    required String uuid,
    int? code,
  }) async {
    await apiPost(
      url: '$_recruitment/$uuid/send-verification-email',
      controller: controllers,
      data: {},
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> getIsVerified({
    required BaseControllers controllers,
    required String uuid,
    int? code,
  }) async {
    await apiFetch(
      url: '$_recruitment/check-email-verified?uuid=$uuid',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> getGlobalConfig({
    required BaseControllers controllers,
    required String key,
    int? code,
  }) async {
    await apiFetch(
      url: '$_globalConfig/$key',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  // Terminasi
  Future<void> performSubmitTerminasi({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: _agentTermination,
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> getTerminationList({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: _agentTermination,
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> cancelTermination({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPut(
      url: '$_agentTermination/cancel',
      controller: controllers,
      code: code ?? 0,
      debug: false,
      data: data,
    );
  }

  Future<void> getTerminationEligibleCandidates({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await apiFetch(
      url: _agentTerminationCandidates,
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> performSubmitPolicyTransfer({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await apiPost(
      url: _agentPolicyTransfer,
      controller: controllers,
      data: data,
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> changePolicyTransfer({
    required BaseControllers controllers,
    required var terminationId,
    required var targetAgentCode,
    int? code,
  }) async {
    await apiPut(
      url:
          '$_agentPolicyTransfer/$terminationId?targetAgentCode=$targetAgentCode',
      controller: controllers,
      data: {},
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> getPolicyTransferList({
    required BaseControllers controllers,
    required String? agentCode,
    int? code,
  }) async {
    await apiFetch(
      url: '$_agentPolicyTransfer/assigned/$agentCode',
      controller: controllers,
      code: code ?? 0,
      debug: false,
    );
  }

  Future<void> approvePolicyTransfer({
    required BaseControllers controllers,
    required var terminationId,
    int? code,
  }) async {
    await apiPut(
      url: '$_agentPolicyTransfer/$terminationId/approve',
      controller: controllers,
      data: {},
      debug: false,
      code: code ?? 0,
    );
  }

  Future<void> rejectPolicyTransfer({
    required BaseControllers controllers,
    required var terminationId,
    int? code,
  }) async {
    await apiPut(
      url: '$_agentPolicyTransfer/$terminationId/reject',
      controller: controllers,
      data: {},
      debug: false,
      code: code ?? 0,
    );
  }
}
