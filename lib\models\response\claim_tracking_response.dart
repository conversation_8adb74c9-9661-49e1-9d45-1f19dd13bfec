import 'dart:convert';

import 'package:pdl_superapp/models/claim_tracking_model.dart';
import 'package:pdl_superapp/models/pageable_model.dart';
import 'package:pdl_superapp/models/sort_model.dart';

ClaimTrackingResponse claimTrackingResponseFromJson(String str) =>
    ClaimTrackingResponse.fromJson(json.decode(str));

String claimTrackingResponseToJson(ClaimTrackingResponse data) =>
    json.encode(data.toJson());

class ClaimTrackingResponse {
  List<ClaimTrackingModel>? content;
  Pageable? pageable;
  bool? last;
  int? totalPages;
  int? totalElements;
  int? size;
  int? number;
  Sort? sort;
  bool? first;
  int? numberOfElements;
  bool? empty;

  ClaimTrackingResponse({
    this.content,
    this.pageable,
    this.last,
    this.totalPages,
    this.totalElements,
    this.size,
    this.number,
    this.sort,
    this.first,
    this.numberOfElements,
    this.empty,
  });

  factory ClaimTrackingResponse.fromJson(Map<String, dynamic> json) =>
      ClaimTrackingResponse(
        content: json["content"] == null
            ? []
            : List<ClaimTrackingModel>.from(
                json["content"]!.map((x) => ClaimTrackingModel.fromJson(x)),
              ),
        pageable: json["pageable"] == null
            ? null
            : Pageable.fromJson(json["pageable"]),
        last: json["last"],
        totalPages: json["totalPages"],
        totalElements: json["totalElements"],
        size: json["size"],
        number: json["number"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        first: json["first"],
        numberOfElements: json["numberOfElements"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
        "pageable": pageable?.toJson(),
        "last": last,
        "totalPages": totalPages,
        "totalElements": totalElements,
        "size": size,
        "number": number,
        "sort": sort?.toJson(),
        "first": first,
        "numberOfElements": numberOfElements,
        "empty": empty,
      };
}
