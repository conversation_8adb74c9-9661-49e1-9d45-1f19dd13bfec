import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

import '../../base/base_detail_page.dart';
import '../../components/table_card.dart';
import '../../controllers/widget/persistensi_page_controller.dart';
import '../../models/persistensi_model.dart';
import '../../utils/keys.dart';
import '../../utils/utils.dart';

class PersistensiPage extends StatelessWidget {
  PersistensiPage({super.key});

  final PersistensiPageController controller = Get.put(
    PersistensiPageController(
      isShowOtherAgent: (Get.parameters['mode'] ?? "0").toString() == "1",
    ),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'persistency_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          if (controller.isLoading.isTrue) {
            return const Center(child: CircularProgressIndicator());
          }
          return Column(
            children: [
              SizedBox(height: 10),
              // Show tabs for non-BP roles or BD roles
              if (controller.level != kLevelBP) _buildTabSelector(context),

              // Content based on role and selected tab
              _buildContent(),
            ],
          );
        }),
      ),
    );
  }

  // Tab selector for non-BP roles
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 0
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 0
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 1
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 1
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    controller.level == kLevelBD ? 'Group' : 'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role, show only individu content
    if (controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      if (controller.selectedSection.value == 0) {
        return _buildIndividuContent();
      } else {
        return _buildTeamContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.individuController.persistensiData.value ==
                null)
              const Center(child: Text('No data available'))
            else
              _buildPersistencyData(
                controller.individuController.persistensiData.value!,
              ),
          ],
        ),
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.teamController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAgentPersistencyTables(),
          ],
        ),
      );
    });
  }

  // Build persistency data display
  Widget _buildPersistencyData(PersistensiModel data) {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPersistenceRow(
          'Persistensi-13',
          controller.individuController.formatPercentage(data.persistency13),
        ),
        _buildPersistenceRow(
          'Persistensi-25',
          controller.individuController.formatPercentage(data.persistency25),
        ),
        _buildPersistenceRow(
          'Persistensi-37',
          controller.individuController.formatPercentage(data.persistency37),
        ),
        _buildPersistenceRow(
          'Persistensi-49',
          controller.individuController.formatPercentage(data.persistency49),
        ),
        _buildPersistenceRow(
          'Persistensi-61',
          controller.individuController.formatPercentage(data.persistency61),
        ),
      ],
    );
  }

  // Build tables for each agent in the team/group
  Widget _buildAgentPersistencyTables() {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          (!controller.showFullData.value &&
                      controller.teamController.persistensiDataList.length > 3
                  ? controller.teamController.persistensiDataList.take(3)
                  : controller.teamController.persistensiDataList)
              .map((agent) {
                return _buildAgentPersistencyTable(agent);
              })
              .toList(),
    );
  }

  // Build a table card for a single agent's persistency data
  Widget _buildAgentPersistencyTable(PersistensiModel agent) {
    String agentType = agent.agentLevel;
    // String agentType = "";
    // switch (agent.type) {
    //   case "leader":
    //     agentType = "BM";
    //     break;
    //   case "agent":
    //     agentType = "BD";
    //     break;
    //   default:
    //     agentType = "BP";
    //     break;
    // }
    if (agent.name == '') {
      return Container();
    }
    final agentTitle = "$agentType ${agent.name}";
    return SizedBox(
      width: Get.width,
      child: TableCard(
        avatar:
            controller.agentCode == agent.agentCode ? agent.agentPhoto : null,
        title: agentTitle,
        headers: ["P-13", "P-25", "P-37", "P-49", "P-61"],
        rows: [
          [
            controller.teamController.formatPercentage(agent.persistency13),
            controller.teamController.formatPercentage(agent.persistency25),
            controller.teamController.formatPercentage(agent.persistency37),
            controller.teamController.formatPercentage(agent.persistency49),
            controller.teamController.formatPercentage(agent.persistency61),
          ],
        ],
        // Enable horizontal scrolling
        horizontalScrollable: true,
        // Set minimum column widths
        minColumnWidths: {
          0: (Get.width * .92) / 5, // P-13 column
          1: (Get.width * .92) / 5, // P-25 column
          2: (Get.width * .92) / 5, // P-37 column
          3: (Get.width * .92) / 5, // P-49 column
          4: (Get.width * .92) / 5, // P-61 column
        },
      ),
    );
  }

  Widget _buildPersistenceRow(String title, String percentage) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
      ],
    );
  }
}
