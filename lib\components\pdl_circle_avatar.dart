import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PdlCircleAvatar extends StatelessWidget {
  const PdlCircleAvatar({
    super.key,
    required this.source,
    this.radius = 100,
    this.width,
    this.height,
    this.border,
    this.errorWidget,
  });
  final String source;
  final double radius;
  final double? width;
  final double? height;
  final bool? border;
  final Widget? errorWidget;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius),
        border:
            border == true
                ? Border.all(
                  color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                  width: 2,
                )
                : null,
      ),
      width: width,
      height: height,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: CachedNetworkImage(
          imageUrl: source,
          fit: BoxFit.cover,
          errorWidget:
              (context, url, error) =>
                  errorWidget ??
                  Container(
                    color: Colors.grey[300],
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey[600],
                    ),
                  ),
        ),
      ),
    );
  }
}
