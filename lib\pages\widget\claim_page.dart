import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/claim_item.dart';
import 'package:pdl_superapp/components/filter_bottom_sheet.dart';
import 'package:pdl_superapp/controllers/widget/claim_page_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/num_ext.dart';

class ClaimPage extends StatelessWidget {
  ClaimPage({super.key});

  final ClaimPageController controller = Get.put(
    ClaimPageController(
      agentCode: Get.parameters['agentCode'],
      withDownline:
          int.tryParse(Get.parameters['withDownline'].toString()) ?? 0,
    ),
    tag: "claim-page-${DateTime.timestamp()}",
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'claim_status_str'.tr,
      scrollPhysics: NeverScrollableScrollPhysics(),
      child: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height - (Get.statusBarHeight - paddingSmall),
            padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
            child: Obx(() {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Search and filter - Always visible
                  _buildSearchFilterBar(context),

                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: () async => controller.refreshData(),
                      child: SingleChildScrollView(
                        controller: controller.scrollController,
                        child: Column(
                          children: [
                            // Loading indicator
                            if (controller.isLoading.value &&
                                controller.claimTrackingList.isEmpty)
                              SizedBox(
                                width: Get.width,
                                height: 200,
                                child: const Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),

                            // Error message
                            if (controller.hasError.value)
                              Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.error_outline,
                                      size: 48,
                                      color: Colors.red,
                                    ),
                                    const SizedBox(height: paddingSmall),
                                    Text(
                                      'Error: ${controller.errorMessage.value}',
                                    ),
                                    const SizedBox(height: paddingMedium),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        ElevatedButton(
                                          onPressed:
                                              () => controller.refreshData(),
                                          child: Text('retry_str'.tr),
                                        ),
                                        const SizedBox(width: paddingMedium),
                                        if (controller
                                                .activeFilters
                                                .value
                                                .isNotEmpty ||
                                            controller.searchQuery.isNotEmpty)
                                          OutlinedButton(
                                            onPressed: () {
                                              controller.resetFilters();
                                              controller.refreshData();
                                              controller.searchTextController
                                                  .clear();
                                            },
                                            child: const Text(
                                              'Reset Filter & Coba Lagi',
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                            // Empty state
                            if (!controller.isLoading.value &&
                                !controller.hasError.value &&
                                controller.claimTrackingList.isEmpty)
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(paddingMedium),
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.hourglass_empty,
                                        size: 48,
                                        color: Colors.grey.shade400,
                                      ),
                                      const SizedBox(height: paddingMedium),
                                      const Text(
                                        "Kami tidak menemukan data yang anda cari",
                                      ),
                                      const SizedBox(height: paddingSmall),
                                      if (controller
                                              .activeFilters
                                              .value
                                              .isNotEmpty ||
                                          controller.searchQuery.isNotEmpty)
                                        TextButton(
                                          onPressed: () {
                                            controller.resetFilters();
                                            controller.refreshData();
                                            controller.searchTextController
                                                .clear();
                                          },
                                          child: const Text('Reset Filter'),
                                        ),
                                    ],
                                  ),
                                ),
                              ),

                            // No results message
                            if (!controller.isLoading.value &&
                                !controller.hasError.value &&
                                controller.claimTrackingList.isNotEmpty &&
                                controller.claimTrackingList.isEmpty)
                              Padding(
                                padding: const EdgeInsets.all(paddingLarge),
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.search_off,
                                      size: 48,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: paddingMedium),
                                    Text(
                                      'Tidak ada hasil yang sesuai dengan pencarian',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    if (controller
                                            .activeFilters
                                            .value
                                            .isNotEmpty ||
                                        controller.searchQuery.isNotEmpty)
                                      TextButton(
                                        onPressed:
                                            () => controller.resetFilters(),
                                        child: const Text('Reset pencarian'),
                                      ),
                                  ],
                                ),
                              ),
                            // Claims list
                            if (!controller.isLoading.value &&
                                !controller.hasError.value &&
                                controller.claimTrackingList.isNotEmpty)
                              Column(
                                children: [
                                  ListView.separated(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    padding: EdgeInsets.zero,
                                    itemCount:
                                        controller.claimTrackingList.length,
                                    separatorBuilder:
                                        (context, index) =>
                                            const Divider(thickness: 3),
                                    itemBuilder: (context, index) {
                                      final claim =
                                          controller.claimTrackingList[index];
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 16.0,
                                        ),
                                        child: ClaimItem(
                                          agentCode: claim.agentCode,
                                          agentName: claim.agentName,
                                          userLevel: controller.level,
                                          claimId: claim.claimId,
                                          policyHolder: claim.policyHolder,
                                          policyNumber: claim.policyNumber,
                                          issueDate: controller.formatDate(
                                            claim.requestedDate,
                                          ),
                                          claimAmount:
                                              claim.amount.formatCurrencyId(),
                                          note: claim.remark,
                                          bulletNotes:
                                              claim.diagnose.isNotEmpty
                                                  ? [claim.diagnose]
                                                  : [],
                                          status: claim.claimStatus,
                                        ),
                                      );
                                    },
                                  ),
                                  // Loading indicator for pagination
                                  if (controller.isLoadingMore.value)
                                    SizedBox(
                                      width: Get.width,
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    ),
                                  SizedBox(height: paddingExtraLarge),
                                ],
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
          // Scroll to top button
          Obx(() {
            return AnimatedPositioned(
              duration: const Duration(milliseconds: 300),
              bottom: controller.showScrollToTopButton.value ? 20 : -60,
              right: 20,
              child: FloatingActionButton(
                mini: true,
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                onPressed: controller.scrollToTop,
                child: const Icon(Icons.keyboard_arrow_up),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSearchFilterBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: paddingMedium, bottom: paddingMedium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        spacing: paddingMedium,
        children: [
          // Search bar - back to original design
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: paddingSmall,
                vertical: paddingSmall,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(radiusSmall),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                spacing: paddingSmall,
                children: [
                  Icon(Icons.search, color: Colors.grey.shade600),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'find_name_number_str'.tr,
                        border: InputBorder.none,
                        isDense: true,
                        isCollapsed: true,
                        contentPadding: EdgeInsets.symmetric(
                          vertical: paddingExtraSmall,
                        ),
                        fillColor: Colors.transparent,
                      ),
                      onChanged: (value) {
                        controller.searchQuery.value = value;
                        // Trigger search when typing (with debounce-like behavior)
                        if (value.length > 2) {
                          controller.applyFilters();
                        } else if (value.isEmpty) {
                          controller.applyFilters();
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          InkWell(
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder:
                    (context) => FilterBottomSheet(
                      initialFilters: controller.activeFilters.value,
                      userLevel: controller.userLevel,
                    ),
              ).then((result) {
                if (result != null) {
                  controller.activeFilters.value = result;
                  // Apply filters after setting the new values
                  controller.applyFilters();
                }
              });
            },
            borderRadius: BorderRadius.circular(radiusSmall),
            child: Container(
              height: 48,
              padding: const EdgeInsets.symmetric(horizontal: paddingSmall),
              decoration: BoxDecoration(
                color:
                    controller.activeFilters.value.isNotEmpty
                        ? const Color(0xFFE3F2FD)
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(radiusSmall),
                border: Border.all(
                  color:
                      controller.activeFilters.value.isNotEmpty
                          ? const Color(0xFF1976D2)
                          : Colors.grey.shade300,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.filter_list,
                    size: 20,
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Filter',
                    style: TextStyle(
                      color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                      fontWeight:
                          controller.activeFilters.value.isNotEmpty
                              ? FontWeight.w500
                              : FontWeight.normal,
                    ),
                  ),
                  if (controller.activeFilters.value.isNotEmpty &&
                      controller.activeFilters.value['status'] != null &&
                      (controller.activeFilters.value['status'] as List)
                          .isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(left: 6),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1976D2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${(controller.activeFilters.value['status'] as List).length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
