import 'package:intl/intl.dart';

extension StringExt on String {
  String toCapitalize() {
    return split(' ')
        .map(
          (word) =>
              word.isNotEmpty
                  ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                  : '',
        )
        .join(' ');
  }

  bool inList(List<String> values) {
    for (var value in values) {
      if (value.toLowerCase() == toLowerCase()) {
        return true;
      }
    }
    return false;
  }

  String formatDate({format = "dd/MMM/yyyy"}) {
    try {
      final date = DateTime.parse(this);
      return DateFormat(format).format(date);
    } catch (e) {
      return this;
    }
  }

  String getInitials() {
    final parts = trim().split(RegExp(r'\s+'));
    if (parts.length == 1) {
      return (parts[0].substring(0, 1) + parts[0].substring(1, 2))
          .toUpperCase();
    } else {
      return (parts[0].substring(0, 1) + parts[1].substring(0, 1))
          .toUpperCase();
    }
  }

  String get getRoleOnly {
    return replaceAll('ROLE_AGE_', '');
  }
}
