import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/menu_inbox_models.dart';
import 'package:pdl_superapp/models/response/count_unread_inbox_response.dart';
import 'package:pdl_superapp/models/response/inbox_response.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum TypeInbox { recruitment, demosi, terminasi, rejoin }

class InboxListController extends BaseControllers {
  RxList<MenuInboxModel> menuInbox = <MenuInboxModel>[].obs;
  final selectedMenu = Rxn<MenuInboxModel>();
  final inboxResp = Rxn<InboxResponse>();
  RxList<InboxModel> inboxes = <InboxModel>[].obs;
  RxList<String> ids = <String>[].obs;
  String level = '';
  bool isBdmPlus = false;

  RxInt totalPages = 0.obs;
  RxInt currentPage = 0.obs;

  RxBool isArchive = false.obs;
  RxBool isDeleted = false.obs;
  final q = Rxn<String>();
  final trxType = Rxn<String>();

  late SharedPreferences prefs;

  void setSelectedMenu(MenuInboxModel menu) {
    selectedMenu.value = menu;
    if (menu.title.toLowerCase() == 'semua inbox') {
      trxType.value = null;
      isArchive.value = false;
      isDeleted.value = false;
      getInbox(isRefresh: true);
    } else if (menu.title.toLowerCase() == 'arsip') {
      trxType.value = null;
      isArchive.value = true;
      isDeleted.value = true;
      getInbox(isRefresh: true);
    } else if (menu.title.toLowerCase() == 'sampah') {
      trxType.value = null;
      isArchive.value = false;
      isDeleted.value = true;
      getInbox(isRefresh: true);
    } else {
      trxType.value = menu.code;
      isArchive.value = false;
      isDeleted.value = false;
      getInbox(isRefresh: true);
    }
  }

  @override
  Future<void> onInit() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    if (level == '-' || level.isEmpty) {
      final level = (prefs.getString(kStorageUserLevelComplete) ?? '');
      isBdmPlus = level.inList([
        kUserLevelBdm,
        kUserLevelBDD,
        kUserLevelABDD,
        kUserLevelHOS,
        kUserLevelCAO,
        kUserLevelARA,
      ]);
    }
    getInbox(isRefresh: true);
    reqGetUnreadCountInbox();
    _addMenuInbox();
    selectedMenu.value = menuInbox.first;
    super.onInit();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetInbox:
        _onSuccessGetInbox(response);
        break;
      case kReqReadInbox:
        _onSuccessReadInbox(response);
        break;
      case kReqArchiveInbox:
        _onSuccessArchiveInbox();

        break;
      case kReqDeleteInbox:
        _onSuccessDeleteInbox();

        break;
      case kReqCountUnreadInbox:
        _getCountUnread(response);
        break;
      default:
    }
  }

  void setSingleIds(String id) {
    ids.clear();
    ids.add(id);
  }

  void setSelectedIds() {
    ids.clear();
    ids.addAll(
      inboxes
          .where((p0) => p0.inboxSelected)
          .map((element) => '${element.id}')
          .toList(),
    );
  }

  void _onSuccessReadInbox(response) {
    for (final item in ids) {
      final index = inboxes.indexWhere(
        (element) => element.id.toString() == item,
      );
      inboxes[index].isRead = !(inboxes[index].isRead ?? false);
      inboxes.refresh();
    }

    if (ids.length == 1 && !inboxes.any((e) => e.inboxSelected)) {
      goToDetail(id: ids.first);
    }

    _unselectedAllInbox();
    reqGetUnreadCountInbox();
  }

  void _unselectedAllInbox() {
    for (var inbox in inboxes) {
      inbox.inboxSelected = false;
    }
    inboxes.refresh();
  }

  void goToDetail({required String id}) {
    Get.toNamed(
      Routes.MENU_DETAIL_INBOX,
      arguments: {
        'data': inboxes.firstWhereOrNull(
          (element) => element.id.toString() == id,
        ),
        'isFromTrash': selectedMenu.value?.title.toLowerCase() == 'sampah',
      },
    );
  }

  void _onSuccessGetInbox(response) {
    inboxResp.value = InboxResponse.fromJson(response);
    inboxes.addAll(inboxResp.value?.content ?? []);
    totalPages.value =
        (inboxResp.value?.totalPages ?? 1) -
        1; // dikurangi 1 karena current page nya dimulai dari 0, tapi total pages dimulai dari 1
  }

  void _getCountUnread(response) {
    int allCount = 0;
    for (final item in response) {
      final count = CountUnreadInboxResponse.fromJson(item);
      final exist = menuInbox.any((element) => element.code == count.trxType);
      if (exist) {
        final unReadCount = count.count ?? 0;
        allCount += unReadCount;
        menuInbox[menuInbox.indexWhere(
              (element) => element.code == count.trxType,
            )]
            .inboxCount = unReadCount;
      }
    }
    menuInbox[0].inboxCount = allCount;
    menuInbox.refresh();
  }

  void _onSuccessArchiveInbox() {
    for (final item in ids) {
      final index = inboxes.indexWhere(
        (element) => element.id.toString() == item,
      );
      _removeInboxFromList(index);
    }

    if (selectedMenu.value?.title.toLowerCase() != 'arsip') {
      showToast(
        text: '${ids.length} ${'message_archived_str'.tr}',
        onCancel: () async {
          await reqBulkArchiveInbox();
          getInbox(isRefresh: true);
        },
      );
    }

    reqGetUnreadCountInbox();
  }

  void _onSuccessDeleteInbox() {
    for (final item in ids) {
      final index = inboxes.indexWhere(
        (element) => element.id.toString() == item,
      );
      _removeInboxFromList(index);
    }

    if (selectedMenu.value?.title.toLowerCase() != 'sampah') {
      showToast(
        text: '${ids.length} ${'message_deleted_str'.tr}',
        onCancel: () async {
          await reqBulkDeleteInbox(
            isHardDelete: selectedMenu.value?.title.toLowerCase() == 'sampah',
          );
          getInbox(isRefresh: true);
        },
      );
    }

    reqGetUnreadCountInbox();
  }

  void _removeInboxFromList(int index) {
    inboxes.removeAt(index);
    inboxes.refresh();
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    Get.snackbar(
      'Gagal',
      response.body['message'] ?? 'Terjadi Kesalahan harap ulangi kembali',
      colorText: Colors.white,
      backgroundColor: Colors.red,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void showToast({required String text, required VoidCallback onCancel}) {
    Get.snackbar(
      '',
      '',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: kLine,
      titleText: SizedBox.shrink(),
      messageText: Row(
        children: [
          Expanded(
            child: Text(
              text,
              style: Theme.of(
                Get.context!,
              ).textTheme.bodyMedium?.copyWith(color: Colors.black),
            ),
          ),
          InkWell(
            onTap: onCancel,
            child: Row(
              children: [
                Utils.cachedSvgWrapper(
                  'icon/ic-linear-restart.svg',
                  color: kColorGlobalBlue,
                ),
                SizedBox(width: paddingSmall),
                Text(
                  'cancel_str'.tr,
                  style: Theme.of(
                    Get.context!,
                  ).textTheme.bodyMedium?.copyWith(color: kColorGlobalBlue),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> getInbox({bool isRefresh = false}) async {
    setLoading(true);
    if (isRefresh) {
      currentPage.value = 0;
      totalPages.value = 0;
      inboxes.clear();
    }
    Map<String, dynamic> queryParams = {
      'page': currentPage.value,
      'size': 10,
      'inboxType': 'INBOX',
      'isArchived': isArchive.value,
      'deleted': isDeleted.value,
    };

    if (trxType.value != null) {
      if (trxType.value!.contains('RECRUITMENT')) {
        queryParams['trxType'] =
            'RECRUITMENT_BP&trxType=RECRUITMENT_BM&trxType=RECRUITMENT_BD';
      }
      if (trxType.value!.contains('REJOIN')) {
        queryParams['trxType'] = 'REJOIN_BP&trxType=REJOIN_BM';
      } else {
        queryParams['trxType'] = trxType.value;
      }
    }

    if (q.value != null && q.value!.trim().isNotEmpty) {
      queryParams['q'] = q.value;
    }

    await api.getInbox(
      controllers: this,
      code: kReqGetInbox,
      query: queryParams,
    );
  }

  Future<void> reqGetUnreadCountInbox() async {
    await api.getCountUnreadInbox(
      controllers: this,
      code: kReqCountUnreadInbox,
    );
  }

  Future<void> reqBulkReadInbox() async {
    await api.postReadInbox(controllers: this, code: kReqReadInbox, ids: ids);
  }

  Future<void> reqBulkArchiveInbox() async {
    await api.postArchiveInbox(
      controllers: this,
      code: kReqArchiveInbox,
      ids: ids,
    );
  }

  Future<void> reqBulkDeleteInbox({required bool isHardDelete}) async {
    await api.deleteInbox(
      isHardDelete: isHardDelete,
      controllers: this,
      code: kReqDeleteInbox,
      ids: ids,
    );
  }

  String iconInbox(InboxModel inbox) {
    final type = (inbox.inboxType ?? '').toLowerCase();
    if (type.contains(TypeInbox.recruitment.name)) {
      return 'icon/sub-modul-recruitment.svg';
    } else if (type.contains(TypeInbox.terminasi.name)) {
      return 'icon/sub-modul-terminasi.svg';
    } else if (type.contains(TypeInbox.demosi.name)) {
      return 'icon/sub-modul-demosi.svg';
    } else if (type.contains(TypeInbox.rejoin.name)) {
      return 'icon/sub-modul-rejoin.svg';
    }
    return 'icon/sub-modul-promosi.svg';
  }

  void _addMenuInbox() {
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-letter",
        inboxSelected: true,

        title: "Semua Inbox",
        code: '',
      ),
    );

    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-user-plus",
        inboxSelected: false,
        title: "Rekrutmen Agen Baru",
        code: 'RECRUITMENT_$level',
      ),
    );
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-promotion",
        inboxSelected: false,
        title: "Promosi",
        code: 'PROMOSI_$level',
      ),
    );
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-user-minus",
        inboxSelected: false,
        title: "Demosi",
        code: 'DEMOSI_$level',
      ),
    );
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-user-cross",
        inboxSelected: false,
        title: "Terminasi",
        code: 'TERMINASI_$level',
      ),
    );
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-restart",
        inboxSelected: false,
        title: "Bergabung Kembali",
        code: 'REJOIN_$level',
      ),
    );
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-verified-check",
        inboxSelected: false,
        title: "Lisensi",
        code: 'LISENSI',
      ),
    );
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-money-calculator-20",
        inboxSelected: false,
        title: "BOP",
        code: 'BOP',
      ),
    );
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-Notes",
        inboxSelected: false,
        title: "Arsip",
        code: 'ARCHIVE',
      ),
    );
    menuInbox.add(
      MenuInboxModel(
        icon: "ic-bold-trash-bin",
        inboxSelected: false,
        title: "Sampah",
        code: 'TRASH',
      ),
    );

    menuInbox.refresh();
  }
}
