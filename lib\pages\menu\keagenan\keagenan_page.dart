// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/custom_card_agency.dart';
import 'package:pdl_superapp/components/filter_button.dart';
import 'package:pdl_superapp/components/icon_menu.dart';
import 'package:pdl_superapp/components/item_action_menu.dart';
import 'package:pdl_superapp/components/material_bottom_sheet.dart';
import 'package:pdl_superapp/components/method_bottom_sheet_card.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/recruitment_api_card.dart';
import 'package:pdl_superapp/components/state/empty_state_view.dart';
import 'package:pdl_superapp/components/terminasi_type_bottom_sheet.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/keagenan_controller.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/pages/menu/keagenan/widgets/rejoin_card.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class KeagenanPage extends StatefulWidget {
  const KeagenanPage({super.key});

  @override
  State<KeagenanPage> createState() => _KeagenanPageState();
}

class _KeagenanPageState extends State<KeagenanPage> {
  final KeagenanController controller = Get.put(KeagenanController());
  final ScrollController scrollController = ScrollController();
  final debouncer = Debouncer(delay: const Duration(milliseconds: 700));

  @override
  void initState() {
    Future.delayed(Duration.zero, () => FocusScope.of(context).unfocus());
    super.initState();
    setupScrollListener();
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      // Check if user scrolled to bottom with some threshold
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 200) {
        // Load more data when near bottom
        controller.loadMoreData();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Keagenan',
      scrollController: scrollController,
      isActionActive: true,
      actions: [
        ItemActionMenu(
          onTap: () => Get.toNamed(Routes.MENU_INBOX),
          child: Stack(
            children: [
              Utils.cachedSvgWrapper(
                'icon/ic-linear-letter.svg',
                color: Colors.white,
              ),
            ],
          ),
        ),
      ],
      controller: controller,
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _title('title_keagenan'.tr),
          GridView.count(
            crossAxisCount: 4,
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            children: [
              IconMenu(
                onTap: () => Get.toNamed(Routes.KEAGENAN_LIST),
                iconUrl: 'icon/sub-modul-recruitment.svg',
                title: 'recruit_str'.tr,
              ),
              IconMenu(
                onTap: () => _showModalRejoin(context),
                iconUrl: 'icon/sub-modul-rejoin.svg',
                title: 'rejoin_str'.tr,
              ),
              IconMenu(
                onTap:
                    () => showModalBottomSheetMaterial(
                      context,
                      child: _terminasiTypeBottomSheet(controller.level),
                      title: "title_select_termination_type".tr,
                    ),
                iconUrl: 'icon/sub-modul-terminasi.svg',
                title: 'termination_str'.tr,
              ),
            ],
          ),
          SizedBox(height: paddingMedium),
          Divider(thickness: 6),
          _productivity(context),
          SizedBox(height: paddingMedium),
          Divider(thickness: 6),
          _sliderCard(),
          Obx(
            () => Padding(
              padding: EdgeInsets.symmetric(horizontal: paddingMedium),
              child: TitleWidget(
                title:
                    '${'list_candidates_str'.tr} ${controller.selectedMenuCandidates.value}',
              ),
            ),
          ),

          SizedBox(height: paddingMedium),
          Container(
            margin: EdgeInsets.symmetric(horizontal: paddingMedium),
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Get.isDarkMode ? kColorGlobalBgDarkBlue : Colors.grey[200],
              borderRadius: BorderRadius.circular(40),
            ),
            child: Obx(
              () => Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildRecruitmentTab("active_approval_str".tr, 0),
                  SizedBox(width: 8),
                  _buildRecruitmentTab("history_approval_str".tr, 1),
                ],
              ),
            ),
          ),
          SizedBox(height: paddingMedium),
          _searchBar(),
          _paginationInfo(),
          Obx(() => _listDataSlider()),
        ],
      ),
    );
  }

  Widget _listDataSlider() {
    final selectedMenu = controller.selectedMenuCandidates.value;

    if (selectedMenu == 'Promosi' || selectedMenu == 'Demosi') {
      return SizedBox.shrink();
    }

    if (selectedMenu == 'Bergabung Kembali') {
      return _rejoinList();
    }

    if (selectedMenu == 'Rekrut') {
      return _recruitmentList();
    }

    return _recruitmentList();
  }

  void _showModalRejoin(BuildContext context) {
    showModalBottomSheetMaterial(
      context,
      child: _methodRejoinBottomSheet(controller),
    );
  }

  SizedBox _methodRejoinBottomSheet(KeagenanController controller) {
    return SizedBox(
      width: Get.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingMedium),
          MethodBottomSheetCard(
            iconUrl: 'icon/ic-menu-rejoin-request.svg',
            title: 'list_submission_rejoin_str'.tr,
            description: 'see_list_agent_rejoin_str'.tr,
            onTap: () {
              Get.back();
              Get.toNamed(Routes.REJOIN, arguments: {'is_req_new': false});
            },
          ),
          SizedBox(height: paddingMedium),
          for (final item in controller.rejoinLevelAvailables())
            Container(
              margin: EdgeInsets.only(bottom: paddingMedium),
              child: MethodBottomSheetCard(
                iconUrl: 'icon/ic-menu-rejoin-bp.svg',
                title: '${'rejoin_str'.tr} $item',
                description:
                    '${'choose_agent'.tr} $item ${'for_you_to_rely_str'.tr}',
                onTap: () {
                  Get.back();
                  Get.toNamed(
                    Routes.REJOIN_CHOOSE_USER,
                    arguments: {'rejoin_level': item, 'is_req_new': true},
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _paginationInfo() {
    return Obx(() {
      if (controller.totalElements.value == 0) {
        return SizedBox.shrink();
      }

      final currentItems =
          controller.selectedMenuCandidates.value == 'Rekrut'
              ? (controller.activeRecruitmentTabIndex.value == 0
                  ? controller.approvalRecruitmentList.length
                  : controller.approvalRecruitmentHistoryList.length)
              : controller.approvalRejoinList.length;

      return Container(
        margin: EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingSmall,
        ),
        child: Text(
          'Menampilkan $currentItems dari ${controller.totalElements.value} data',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      );
    });
  }

  void _onSearchChanged(String value) {
    switch (controller.selectedMenuCandidates.value) {
      case 'Rekrut':
      case 'Bergabung Kembali':
        debouncer(() {
          FocusScope.of(context).unfocus();
          controller.q.value = value;
          if (controller.activeRecruitmentTabIndex.value == 0) {
            controller.fetchApiListApproval(isRefresh: true);
          } else {
            controller.fetchApiListApprovalHistory(isRefresh: true);
          }
        });
        break;
      default:
    }
  }

  Container _searchBar() {
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: PdlTextField(
              hint: 'Cari nama atau kode',
              onChanged: _onSearchChanged,
              prefixIcon: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-search -2.svg',
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
            ),
          ),
          SizedBox(width: paddingSmall),
          Obx(
            () => FilterButton(
              notificationCount:
                  controller.filterView.value != null &&
                          controller.filterStatus.value != 'Semua'
                      ? 2
                      : 0,
              dismissable: true,
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: paddingLarge),

                  if (controller.selectedMenuCandidates.value !=
                      'Bergabung Kembali') ...[
                    Text(
                      'View',
                      style: TextTheme.of(
                        context,
                      ).bodyLarge?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: paddingSmall),
                    Row(
                      spacing: paddingSmall,
                      children: [
                        for (final item in controller.availableFilterView)
                          Obx(
                            () => ItemFilter(
                              value: item,
                              isSelected: controller.filterView.value == item,
                              onPressed: () {
                                controller.filterView.value = item;
                                controller.getListData(isRefresh: true);
                              },
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: paddingLarge),
                  ],

                  Text(
                    'Status',
                    style: TextTheme.of(
                      context,
                    ).bodyLarge?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: paddingSmall),
                  Wrap(
                    spacing: paddingSmall,
                    runSpacing: paddingExtraSmall,
                    children: [
                      for (final item in controller.availableFilterStatus)
                        Obx(
                          () => ItemFilter(
                            value: item,
                            title: Utils.getStatusTr(item),
                            isSelected: controller.filterStatus.value == item,
                            onPressed: () {
                              controller.filterStatus.value = item;
                              controller.getListData(isRefresh: true);
                            },
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: paddingLarge),
                  Row(
                    spacing: paddingSmall,
                    children: [
                      Expanded(
                        child: PdlButton(
                          onPressed: controller.resetFilter,
                          backgroundColor:
                              Theme.of(context).colorScheme.surface,
                          foregorundColor: kColorTextTersierLight,
                          title: 'Reset',
                        ),
                      ),
                      Expanded(
                        child: PdlButton(
                          onPressed: () {
                            Get.back();
                          },
                          backgroundColor: kColorGlobalBlue,
                          title: 'Terapkan',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              title: 'Filter Daftar Kandidat',
            ),
          ),
        ],
      ),
    );
  }

  SizedBox _sliderCard() {
    return SizedBox(
      width: Get.width,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Wrap(
          spacing: paddingMedium,
          children: [
            Container(),
            for (final item in controller.menuCandidateCard)
              _itemSliderCard(
                title: item['title'],
                startColor: item['startColor'],
                endColor: item['endColor'],
                onTap: () {
                  controller.q.value = null;
                  controller.getListData(isRefresh: true);
                },
              ),
            Container(),
          ],
        ),
      ),
    );
  }

  Obx _itemSliderCard({
    required String title,
    required Color startColor,
    required Color endColor,
    VoidCallback? onTap,
  }) {
    return Obx(
      () => GestureDetector(
        onTap: () {
          controller.selectedMenuCandidates.value = title;
          onTap?.call();
        },
        child: Container(
          width: Get.width / 2.5,
          padding: EdgeInsets.symmetric(vertical: paddingMedium),
          child: CustomCardAgencyWidget(
            title: title,
            startColor: startColor,
            endColor: endColor,
            selected: controller.selectedMenuCandidates.value,
          ),
        ),
      ),
    );
  }

  Widget _productivity(BuildContext context) {
    return SizedBox(
      width: Get.width,

      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TitleWidget(title: 'Produktivitas Rekrut'),
                      Obx(
                        () => Text(
                          '${controller.selectedRekrut.value == 'Berkode Agen' ? 'Berkode Agen' : 'Baru Berlisensi'} (${controller.selectedPeriode.value})',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            height: 2,
                            color:
                                Get.isDarkMode
                                    ? kColorTextTersier
                                    : kColorTextTersierLight,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Obx(
                  () => FilterButton(
                    title: 'Filter Produktivitas',
                    notificationCount: controller.activeFilterCount,
                    dismissable: true,
                    content: Column(
                      children: [
                        _buildFilterOptions(),
                        SizedBox(height: paddingMedium),
                        Container(
                          width: Get.width,
                          padding: EdgeInsets.only(top: paddingMedium),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(bottom: 0),
                            child: Row(
                              children: [
                                Expanded(
                                  child: PdlButton(
                                    title: "Reset",
                                    borderColor: Colors.transparent,
                                    backgroundColor:
                                        Theme.of(context).colorScheme.surface,
                                    foregorundColor:
                                        Get.isDarkMode
                                            ? kColorTextDark
                                            : kColorTextLight,
                                    onPressed: () {
                                      controller.resetFilters();
                                      Get.back();
                                    },
                                  ),
                                ),
                                SizedBox(width: paddingMedium),
                                Expanded(
                                  child: PdlButton(
                                    title: "Terapkan",
                                    onPressed: () {
                                      controller.applyFilters();
                                      Get.back();
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Chart disini
          _buildProductivityChart(),
        ],
      ),
    );
  }

  Container _title(String title) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingMedium),
      child: TitleWidget(title: title),
    );
  }

  Widget _rejoinList() {
    return Obx(() {
      if (controller.approvalRejoinList.isEmpty) {
        return EmptyStateView(
          msg: 'Hmm.. belum ada permintaan bergabung kembali',
        );
      }

      return Column(
        children: [
          ...controller.approvalRejoinList.map((data) {
            return RejoinCard(data: data);
          }),
          // Show loading indicator for pagination
          Obx(() {
            if (controller.isLoadingMore.value) {
              return Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Center(child: CircularProgressIndicator()),
              );
            }
            return SizedBox.shrink();
          }),
          // Show initial loading
          if (controller.isLoadingListApproval.value &&
              controller.approvalRejoinList.isEmpty)
            Center(child: CircularProgressIndicator()),
          // Load more button
          Obx(() {
            if (controller.hasNextPage.value &&
                !controller.isLoadingMore.value &&
                controller.approvalRejoinList.isNotEmpty) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: paddingMedium),
                child: PdlButton(
                  onPressed: controller.loadMoreData,
                  title: 'Muat Lebih Banyak',
                  backgroundColor: Colors.transparent,
                  foregorundColor: kColorPaninBlue,
                  borderColor: kColorPaninBlue,
                ),
              );
            }
            return SizedBox.shrink();
          }),
          SizedBox(height: paddingLarge),
        ],
      );
    });
  }

  Widget _recruitmentList() {
    return Column(
      children: [
        // Tab content
        Obx(() {
          if (controller.activeRecruitmentTabIndex.value == 0) {
            return _buildApprovalContent();
          } else {
            return _buildHistoryContent();
          }
        }),
      ],
    );
  }

  Widget _buildRecruitmentTab(String text, int currentTab) {
    var isSelected = controller.activeRecruitmentTabIndex.value == currentTab;
    return Expanded(
      child: GestureDetector(
        onTap: () => controller.changeActiveRecruitmentTab(currentTab),
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? Theme.of(context).colorScheme.surface
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(40),
            border: isSelected ? Border.all(color: Colors.grey) : null,
          ),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  isSelected
                      ? (Get.isDarkMode ? kColorTextDark : kColorTextLight)
                      : Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildApprovalContent() {
    return Obx(() {
      if (controller.isLoadingListApproval.isTrue) {
        return SizedBox(
          width: Get.width,
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        );
      }
      if (controller.approvalRecruitmentList.isEmpty) {
        return EmptyStateView(msg: 'Hmm.. belum ada permintaan Rekrut');
      }
      return Column(
        children: [
          ...controller.approvalRecruitmentList.map((data) {
            final recruitment = RecruitmentApiModel.fromJson(data.detailData);
            if (recruitment.trxStatus == 'DRAFT') return Container();
            return RecruitmentApiCard(
              recruitment: recruitment,
              onTap: () => controller.goToApproval(recruitment.uuid!),
              fromKeagenan: true,
            );
          }),

          // Show loading indicator for pagination
          Obx(() {
            if (controller.isLoadingMore.value) {
              return Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Center(child: CircularProgressIndicator()),
              );
            }
            return SizedBox.shrink();
          }),
          // Show initial loading
          if (controller.isLoadingListApproval.value &&
              controller.approvalRecruitmentList.isEmpty)
            Center(child: CircularProgressIndicator()),
          // Load more button
          Obx(() {
            if (controller.hasNextPage.value &&
                !controller.isLoadingMore.value &&
                controller.approvalRecruitmentList.isNotEmpty) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: paddingMedium),
                child: PdlButton(
                  onPressed: controller.loadMoreData,
                  title: 'Muat Lebih Banyak',
                  backgroundColor: Colors.transparent,
                  foregorundColor: kColorPaninBlue,
                  borderColor: kColorPaninBlue,
                ),
              );
            }
            return SizedBox.shrink();
          }),
          SizedBox(height: paddingLarge),
        ],
      );
    });
  }

  Widget _buildHistoryContent() {
    return Obx(() {
      if (controller.isLoadingListApprovalHistory.isTrue) {
        return SizedBox(
          width: Get.width,
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        );
      }
      if (controller.approvalRecruitmentHistoryList.isEmpty) {
        return EmptyStateView(msg: 'Hmm.. belum ada riwayat Rekrut');
      }
      return Column(
        children: [
          ...controller.approvalRecruitmentHistoryList.map((data) {
            final recruitment = RecruitmentApiModel.fromJson(data.detailData);
            if (recruitment.trxStatus == 'DRAFT') return Container();
            return RecruitmentApiCard(
              recruitment: recruitment,
              onTap: () => controller.goToApproval(recruitment.uuid!),
              fromKeagenan: true,
            );
          }),

          // Show loading indicator for pagination
          Obx(() {
            if (controller.isLoadingMore.value) {
              return Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Center(child: CircularProgressIndicator()),
              );
            }
            return SizedBox.shrink();
          }),
          // Show initial loading
          if (controller.isLoadingListApprovalHistory.value &&
              controller.approvalRecruitmentHistoryList.isEmpty)
            Center(child: CircularProgressIndicator()),
          // Load more button
          Obx(() {
            if (controller.hasNextPage.value &&
                !controller.isLoadingMore.value &&
                controller.approvalRecruitmentHistoryList.isNotEmpty) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: paddingMedium),
                child: PdlButton(
                  onPressed: controller.loadMoreData,
                  title: 'Muat Lebih Banyak',
                  backgroundColor: Colors.transparent,
                  foregorundColor: kColorPaninBlue,
                  borderColor: kColorPaninBlue,
                ),
              );
            }
            return SizedBox.shrink();
          }),
          SizedBox(height: paddingLarge),
        ],
      );
    });
  }

  Widget _buildFilterOptions() {
    return SizedBox(
      width: Get.width,

      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: paddingMedium),
          // Filter 1: View (Individu/Team)
          _buildFilterRow(
            'View',
            ['Individu', 'Team'],
            controller.selectedView,
            (value) {
              controller.selectedView.value = value;
              controller.applyFilters();
            },
          ),
          SizedBox(height: paddingSmall),

          // Filter 2: Periode (Perbulan/Pertahun)
          _buildFilterRow(
            'Periode',
            ['Perbulan', 'Pertahun'],
            controller.selectedPeriode,
            (value) {
              controller.selectedPeriode.value = value;
              controller.applyFilters();
            },
          ),
          SizedBox(height: paddingSmall),

          // Filter 3: Rekrut (Berkode Agen/Baru Berlisensi)
          _buildFilterRow(
            'Rekrut',
            ['Berkode Agen', 'Baru Berlisensi'],
            controller.selectedRekrut,
            (value) {
              controller.selectedRekrut.value = value;
              controller.applyFilters();
            },
          ),
          SizedBox(height: paddingSmall),

          // Filter 4: Grafik (Line Chart/Bar Chart)
          _buildFilterRow(
            'Grafik',
            ['Line Chart', 'Bar Chart'],
            controller.selectedChart,
            (value) {
              controller.selectedChart.value = value;
              // Note: Bar chart implementation would need additional logic
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilterRow(
    String title,
    List<String> options,
    RxString selectedValue,
    Function(String) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Get.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => Row(
            children:
                options.map((option) {
                  final isSelected = selectedValue.value == option;
                  return GestureDetector(
                    onTap: () => onChanged(option),
                    child: Container(
                      margin: EdgeInsets.only(
                        right: option == options.last ? 0 : paddingSmall,
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: paddingSmall,
                        horizontal: paddingSmall,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? Get.isDarkMode
                                    ? kColorGlobalBgDarkBlue
                                    : kColorBorderLight
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(6),
                        border:
                            isSelected ? Border.all(color: Colors.grey) : null,
                      ),
                      child: Text(
                        option,
                        textAlign: TextAlign.center,
                        style: Get.textTheme.bodySmall?.copyWith(
                          color:
                              Get.isDarkMode ? kColorTextDark : kColorTextLight,
                          fontWeight:
                              isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildProductivityChart() {
    return Obx(() {
      if (controller.chartData.isEmpty) {
        return SizedBox(
          height: 200,
          child: Center(child: CircularProgressIndicator()),
        );
      }

      final isMonthly = controller.selectedPeriode.value == 'Perbulan';
      final isBarChart = controller.selectedChart.value == 'Bar Chart';
      final labels = isMonthly ? controller.months : controller.years;

      return SizedBox(
        height: 250,
        child:
            isMonthly
                ? SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    padding: EdgeInsets.only(
                      top: paddingExtraLarge,
                      right: 40.0,
                    ),
                    width: 800, // Fixed width for monthly data
                    child: AspectRatio(
                      aspectRatio: 3.0,
                      child:
                          isBarChart
                              ? _buildBarChart(labels)
                              : _buildLineChart(labels),
                    ),
                  ),
                )
                : Container(
                  padding: EdgeInsets.only(top: paddingExtraLarge, right: 40.0),
                  width:
                      labels.length <= 5 ? Get.width - 10 : labels.length * 60,
                  child: AspectRatio(
                    aspectRatio: 2.5,
                    child:
                        isBarChart
                            ? _buildBarChart(labels)
                            : _buildLineChart(labels),
                  ),
                ),
      );
    });
  }

  Widget _buildLineChart(List<String> labels) {
    return Obx(
      () => LineChart(
        LineChartData(
          lineTouchData: LineTouchData(
            touchTooltipData: LineTouchTooltipData(
              tooltipRoundedRadius: 4,
              tooltipPadding: const EdgeInsets.all(8),
              tooltipMargin: 0,
              showOnTopOfTheChartBoxArea: true,
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  return LineTooltipItem(
                    '${controller.selectedRekrut.value == 'Berkode Agen' ? 'Berkode Agen' : 'Baru Berlisensi'} \n${barSpot.y.toInt()}',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList();
              },
              fitInsideHorizontally: true,
              fitInsideVertically: true,
            ),
            handleBuiltInTouches: true,
            touchSpotThreshold: 20,
          ),
          clipData: FlClipData.all(),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval:
                controller.maxY.value > 0 ? controller.maxY.value / 5 : 1,
            verticalInterval: 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: const Color.fromRGBO(128, 128, 128, 0.3),
                strokeWidth: 1,
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: const Color.fromRGBO(128, 128, 128, 0.3),
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget:
                    (value, meta) => _bottomTitleWidgets(value, meta, labels),
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval:
                    controller.maxY.value > 0 ? controller.maxY.value / 5 : 1,
                getTitlesWidget: _leftTitleWidgets,
                reservedSize: 50,
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Colors.transparent),
          ),
          minX: 0,
          maxX: (labels.length - 1 + 0.5).toDouble(),
          minY: controller.minY.value,
          maxY: controller.maxY.value,
          lineBarsData: [
            LineChartBarData(
              spots:
                  controller.selectedRekrut.value == 'Berkode Agen'
                      ? controller.chartData
                      : controller.chartDataBerlisensi,
              isCurved: true,
              gradient: LinearGradient(
                colors: [
                  controller.chartColor,
                  controller.chartColor.withValues(alpha: 0.7),
                ],
              ),
              barWidth: 5,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: true),
              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  colors: [
                    controller.chartColor.withValues(alpha: 0.3),
                    controller.chartColor.withValues(alpha: 0.1),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBarChart(List<String> labels) {
    return Obx(
      () => BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: controller.maxY.value,
          minY: controller.minY.value,
          barTouchData: BarTouchData(
            touchTooltipData: BarTouchTooltipData(
              tooltipRoundedRadius: 4,
              tooltipPadding: const EdgeInsets.all(8),
              tooltipMargin: 0,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  '${controller.selectedRekrut.value == 'Berkode Agen' ? 'Berkode Agen' : 'Baru Berlisensi'} \n${rod.toY.toInt()}',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget:
                    (value, meta) => _bottomTitleWidgets(value, meta, labels),
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval:
                    controller.maxY.value > 0 ? controller.maxY.value / 5 : 1,
                getTitlesWidget: _leftTitleWidgets,
                reservedSize: 50,
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Colors.transparent),
          ),
          barGroups:
              controller.selectedRekrut.value == 'Berkode Agen'
                  ? controller.chartData.map((spot) {
                    return BarChartGroupData(
                      x: spot.x.toInt(),
                      barRods: [
                        BarChartRodData(
                          toY: spot.y,
                          color: controller.chartColor,
                          width: 16,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ],
                    );
                  }).toList()
                  : controller.chartDataBerlisensi.map((spot) {
                    return BarChartGroupData(
                      x: spot.x.toInt(),
                      barRods: [
                        BarChartRodData(
                          toY: spot.y,
                          color: controller.chartColor,
                          width: 16,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ],
                    );
                  }).toList(),
        ),
      ),
    );
  }

  Widget _bottomTitleWidgets(
    double value,
    TitleMeta meta,
    List<String> labels,
  ) {
    TextStyle style = TextStyle(
      fontWeight: FontWeight.bold,
      fontStyle: Theme.of(Get.context!).textTheme.bodySmall?.fontStyle,
    );

    if (value.toInt() == value && value >= 0 && value < labels.length) {
      return Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: Text(
          labels[value.toInt()],
          style: style,
          textAlign: TextAlign.center,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _leftTitleWidgets(double value, TitleMeta meta) {
    TextStyle style = TextStyle(
      fontWeight: FontWeight.bold,
      fontStyle: Theme.of(Get.context!).textTheme.bodySmall?.fontStyle,
    );

    if (value == 0) {
      return const Text('');
    }

    return Padding(
      padding: const EdgeInsets.only(right: paddingSmall),
      child: Text(
        value.toInt().toString(),
        style: style,
        textAlign: TextAlign.end,
        maxLines: 1,
      ),
    );
  }

  SizedBox _terminasiTypeBottomSheet(String agentLevel) {
    if (agentLevel == 'BP') {
      return SizedBox(
        width: Get.width,
        child: Column(
          children: [
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-user-cross-rounded.svg',
              title: 'title_self_termination'.tr,
              description: 'sub_title_self_termination'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(
                  Routes.TERMINASI,
                  arguments: {kArgsSelfTermination: true},
                );
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-check.svg',
              title: 'title_policy_transfer_approval'.tr,
              description: 'sub_title_policy_transfer_approval'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.APPROVAL_POLIS_ASSIGNMENT_LIST);
              },
            ),
            SizedBox(height: paddingMedium),
          ],
        ),
      );
    }

    if (agentLevel == 'BM') {
      return SizedBox(
        width: Get.width,
        child: Column(
          children: [
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-user-cross-rounded.svg',
              title: 'title_self_termination'.tr,
              description: 'sub_title_self_termination'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(
                  Routes.TERMINASI,
                  arguments: {kArgsSelfTermination: true},
                );
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-user-two.svg',
              title: 'title_team_termination'.tr,
              description: 'sub_title_team_termination'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.TERMINASI_SELECT_TEAM);
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-documents.svg',
              title: 'title_termination_submission_list'.tr,
              description: 'sub_title_termination_submission_list'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.TERMINASI_LIST);
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-check.svg',
              title: 'title_policy_transfer_approval'.tr,
              description: 'sub_title_policy_transfer_approval'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.APPROVAL_POLIS_ASSIGNMENT_LIST);
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-badge.svg',
              title: 'title_reassign_policy_transfer'.tr,
              description: 'sub_title_reassign_policy_transfer'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.REASSIGN_POLIS_LIST);
              },
            ),
            SizedBox(height: paddingMedium),
          ],
        ),
      );
    }
    if (agentLevel == 'BD') {
      return SizedBox(
        width: Get.width,
        child: Column(
          children: [
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-user-cross-rounded.svg',
              title: 'title_self_termination'.tr,
              description: 'sub_title_self_termination'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(
                  Routes.TERMINASI,
                  arguments: {kArgsSelfTermination: true},
                );
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-user-two.svg',
              title: 'title_team_termination'.tr,
              description: 'sub_title_team_termination'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.TERMINASI_SELECT_TEAM);
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-user-two.svg',
              title: 'title_bm_team_termination'.tr,
              description: 'sub_title_bm_team_termination'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.TERMINASI_SELECT_TEAM);
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-documents.svg',
              title: 'title_termination_submission_list'.tr,
              description: 'sub_title_termination_submission_list'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.TERMINASI_LIST);
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-check.svg',
              title: 'title_policy_transfer_approval'.tr,
              description: 'sub_title_policy_transfer_approval'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.APPROVAL_POLIS_ASSIGNMENT_LIST);
              },
            ),
            SizedBox(height: paddingMedium),
            TerminasiTypeBottomSheet(
              iconUrl: 'icon/ic-dual-badge.svg',
              title: 'title_reassign_policy_transfer'.tr,
              description: 'sub_title_reassign_policy_transfer'.tr,
              onTap: () {
                Get.back();
                Get.toNamed(Routes.REASSIGN_POLIS_LIST);
              },
            ),
            SizedBox(height: paddingMedium),
          ],
        ),
      );
    }
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          SizedBox(height: paddingMedium),
          TerminasiTypeBottomSheet(
            iconUrl: 'icon/ic-dual-user-two.svg',
            title: 'title_team_termination'.tr,
            description: 'sub_title_team_termination'.tr,
            onTap: () {
              Get.back();
              Get.toNamed(Routes.TERMINASI_SELECT_TEAM);
            },
          ),
          SizedBox(height: paddingMedium),
          TerminasiTypeBottomSheet(
            iconUrl: 'icon/ic-dual-user-two.svg',
            title: 'title_bm_team_termination'.tr,
            description: 'sub_title_bm_team_termination'.tr,
            onTap: () {
              Get.back();
              Get.toNamed(Routes.TERMINASI_SELECT_TEAM);
            },
          ),
          SizedBox(height: paddingMedium),
          TerminasiTypeBottomSheet(
            iconUrl: 'icon/ic-dual-user-two.svg',
            title: 'title_bd_team_termination'.tr,
            description: 'sub_title_bd_team_termination'.tr,
            onTap: () {
              Get.back();
              Get.toNamed(Routes.TERMINASI_SELECT_TEAM);
            },
          ),
        ],
      ),
    );
  }
}

class ItemFilter extends StatelessWidget {
  const ItemFilter({
    super.key,
    required this.onPressed,
    required this.value,
    required this.isSelected,
    this.title,
  });
  final VoidCallback onPressed;
  final String value;
  final bool isSelected;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return PdlButton(
      onPressed: onPressed,
      borderColor: isSelected ? null : kColorBorderLight,
      title: title ?? value,
      backgroundColor:
          isSelected
              ? (Get.isDarkMode ? kColorGlobalBgDarkBlue : kColorBorderLight)
              : Colors.transparent,
      foregorundColor: (Get.isDarkMode ? kColorTextDark : kColorTextLight),
    );
  }
}
